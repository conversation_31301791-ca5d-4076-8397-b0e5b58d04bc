package admin

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	tgbotapi "github.com/a19ba14d/telegram-bot-api/v5"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"

	"telegram-bot-api/internal/dao"
	"telegram-bot-api/internal/middleware"
	"telegram-bot-api/internal/model"
	"telegram-bot-api/internal/model/entity"
	"telegram-bot-api/internal/service"
	"telegram-bot-api/internal/tenant"
	"telegram-bot-api/internal/utility"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/yalks/wallet"
)

// State tracking for admin conversations
type AdminState struct {
	ExpectedInputType string
	MessageID         int
	ChatID            int64
	Context           map[string]string
}

// HandleAdminMessageWrapper adapts HandleMessage to the registry.MessageHandler signature
func HandleAdminMessageWrapper(ctx context.Context, message *tgbotapi.Message) (*tgbotapi.MessageConfig, error) {
	handled, err := HandleMessage(ctx, message)
	if err != nil || !handled {
		return nil, err
	}
	// If the message was handled, return nil to indicate no response is needed
	// (the handler already sent the response)
	return nil, nil
}

// HandleMessage processes admin messages based on current state
func HandleMessage(ctx context.Context, message *tgbotapi.Message) (bool, error) {
	// Check if user is admin
	isAdmin, err := service.Admin().IsCurrentUserAdmin(ctx)
	if err != nil {
		// Log as debug since this is expected for certain update types (inline queries, etc.)
		g.Log().Debugf(ctx, "Failed to check admin permission (expected for some update types): %v", err)
		return false, nil
	}
	if !isAdmin {
		return false, nil
	}

	// Get user state
	userState, err := service.UserState().GetUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user state: %v", err)
		return false, nil
	}

	// If user has state, handle based on state
	if userState != nil && strings.HasPrefix(userState.State, "admin_") {
		// Handle based on state
		switch userState.State {
		case "admin_manual_balance":
			return handleManualBalanceInput(ctx, message, userState)
		case "admin_manual_balance_input":
			return handleManualBalanceInputNew(ctx, message, userState)
		case "admin_flow_requirements":
			return handleFlowRequirementsInput(ctx, message, userState)
		case "admin_flow_requirements_input":
			return handleFlowRequirementsInputNew(ctx, message, userState)
		case "admin_withdrawal_reject_reason":
			return handleWithdrawalRejectReasonInput(ctx, message, userState)
		case "admin_withdrawal_reject_ban_reason":
			return handleWithdrawalRejectBanReasonInput(ctx, message, userState)
		case "admin_personal_stats_search":
			return handlePersonalStatsSearchInput(ctx, message, userState)
		case "admin_betting_volume_date_input":
			return handleBettingVolumeDateInput(ctx, message, userState)
		default:
			return false, nil
		}
	}

	// Check for direct personal stats search (admin can search without state)
	// If message starts with @ or is a number, try personal stats search
	if strings.HasPrefix(message.Text, "@") || isNumeric(message.Text) {
		return handleDirectPersonalStatsSearch(ctx, message)
	}

	// Check for daily report date input (uses separate state tracking)
	if err := handleDailyReportTextInput(ctx, message); err != nil {
		g.Log().Errorf(ctx, "Failed to handle daily report text input: %v", err)
	}

	return false, nil
}

// handleManualBalanceInput processes manual balance adjustment input
func handleManualBalanceInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Parse input format: @username amount [彩金] or userid amount [彩金]
	input := strings.TrimSpace(message.Text)

	// Step 1: Format validation
	parts := strings.Fields(input)
	if len(parts) < 2 || len(parts) > 3 {
		// Format error - clear state and show error
		return sendErrorAndContinue(ctx, message.Chat.ID, userState,
			service.I18n().T(ctx, "AdminBalanceFormatErrorStrict"))
	}

	userIdentifier := parts[0]
	amountStr := parts[1]
	var operationType string
	var isBonusFlow bool
	if len(parts) == 3 {
		operationType = strings.TrimSpace(parts[2])
		// Validate operation type - only allow "彩金" or empty
		if operationType != "彩金" {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminInvalidOperationType"))
		}
		isBonusFlow = true
	}

	var targetUser *entity.Users

	// Check if it's a username (@username) or Telegram ID
	if strings.HasPrefix(userIdentifier, "@") {
		// Username format - query from user_backup_accounts table
		username := userIdentifier // Keep the @ prefix for exact match
		targetUser, err := getUserByTelegramUsername(ctx, username)
		if err != nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState, err.Error())
		}
		if targetUser == nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
	} else {
		// Try to parse as Telegram ID
		telegramID, err := strconv.ParseInt(userIdentifier, 10, 64)
		if err != nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminFormatError"))
		}

		user, err := service.User().GetUserByTelegramId(ctx, telegramID)
		if err != nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState, err.Error())
		}
		if user == nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
		targetUser = user
	}

	// Parse amount
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return sendErrorAndContinue(ctx, message.Chat.ID, userState,
			service.I18n().T(ctx, "AdminInvalidAmount"))
	}

	// Use default description
	var description string
	if amount.IsPositive() {
		description = service.I18n().T(ctx, "AdminDefaultIncreaseReason")
	} else {
		description = service.I18n().T(ctx, "AdminDefaultDecreaseReason")
	}

	// Clear user state
	err = service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Execute the balance adjustment
	var adjustErr error
	if isBonusFlow {
		// Adjust flow requirement
		adjustErr = service.Admin().AdjustUserFlowRequirement(ctx, targetUser.Id, amount.String(), description)
	} else {
		// Adjust balance
		adjustErr = service.Admin().AdjustUserBalance(ctx, targetUser.Id, amount.String(), description)
	}

	if adjustErr != nil {
		// Send error message
		text := fmt.Sprintf("%s\n\n%s",
			service.I18n().T(ctx, "AdminAdjustmentFailed"),
			adjustErr.Error())
		return sendAdminMessage(ctx, message.Chat.ID, text)
	}

	// Send success message
	var successText string
	displayName, _ := service.User().GetUserDisplayName(ctx, targetUser.Id)

	if isBonusFlow {
		// Flow adjustment success
		if amount.IsPositive() {
			successText = fmt.Sprintf(service.I18n().T(ctx, "AdminFlowIncreaseSuccess"),
				displayName, amount.String())
		} else {
			successText = fmt.Sprintf(service.I18n().T(ctx, "AdminFlowDecreaseSuccess"),
				displayName, amount.Abs().String())
		}
	} else {
		// Balance adjustment success
		// Get updated balance using wallet manager
		balanceInfo, _ := wallet.Manager().GetBalance(ctx, targetUser.Id, "CNY")
		currentBalance := ""
		if balanceInfo != nil {
			currentBalance = balanceInfo.AvailableBalance.String()
		}

		if amount.IsPositive() {
			successText = fmt.Sprintf(service.I18n().T(ctx, "AdminBalanceIncreaseSuccess"),
				displayName, amount.String(), currentBalance)
		} else {
			successText = fmt.Sprintf(service.I18n().T(ctx, "AdminBalanceDecreaseSuccess"),
				displayName, amount.Abs().String(), currentBalance)
		}
	}

	// Add admin center keyboard
	keyboard := BuildAdminCenterKeyboard(ctx)
	successText = fmt.Sprintf("%s\n\n%s", successText, service.I18n().T(ctx, "AdminCenterTitle"))

	return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, successText, keyboard)
}

// handleFlowRequirementsInput processes flow requirements adjustment input
func handleFlowRequirementsInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Parse input format: @username flow_amount or userid flow_amount (no description allowed)
	input := strings.TrimSpace(message.Text)

	// Step 1: Format validation - must be exactly 2 parts
	parts := strings.Fields(input)
	if len(parts) != 2 {
		// Format error - show specific error message for flow requirements
		return sendErrorAndContinue(ctx, message.Chat.ID, userState,
			service.I18n().T(ctx, "AdminInvalidFlowFormatStrict"))
	}

	userIdentifier := parts[0]
	amountStr := parts[1]

	var targetUser *entity.Users

	// Check if it's a username (@username) or Telegram ID
	if strings.HasPrefix(userIdentifier, "@") {
		// Username format - query from user_backup_accounts table
		username := userIdentifier // Keep the @ prefix for exact match
		targetUser, err := getUserByTelegramUsername(ctx, username)
		if err != nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState, err.Error())
		}
		if targetUser == nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
	} else {
		// Try to parse as Telegram ID
		telegramID, err := strconv.ParseInt(userIdentifier, 10, 64)
		if err != nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminFormatError"))
		}

		user, err := service.User().GetUserByTelegramId(ctx, telegramID)
		if err != nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState, err.Error())
		}
		if user == nil {
			return sendErrorAndContinue(ctx, message.Chat.ID, userState,
				service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
		targetUser = user
	}

	// Parse amount
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return sendErrorAndContinue(ctx, message.Chat.ID, userState,
			service.I18n().T(ctx, "AdminInvalidAmount"))
	}

	// Use default description since no description is allowed in strict format
	var description string
	if amount.IsPositive() {
		description = service.I18n().T(ctx, "AdminDefaultFlowIncreaseReason")
	} else {
		description = service.I18n().T(ctx, "AdminDefaultFlowDecreaseReason")
	}

	// Clear user state
	err = service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Execute the flow adjustment
	adjustErr := service.Admin().AdjustUserFlowRequirement(ctx, targetUser.Id, amount.String(), description)
	if adjustErr != nil {
		// Send error message
		text := fmt.Sprintf("%s\n\n%s",
			service.I18n().T(ctx, "AdminAdjustmentFailed"),
			adjustErr.Error())
		return sendAdminMessage(ctx, message.Chat.ID, text)
	}

	// Send success message
	displayName, _ := service.User().GetUserDisplayName(ctx, targetUser.Id)
	var successText string

	if amount.IsPositive() {
		successText = fmt.Sprintf(service.I18n().T(ctx, "AdminFlowIncreaseSuccess"),
			displayName, amount.String())
	} else {
		successText = fmt.Sprintf(service.I18n().T(ctx, "AdminFlowDecreaseSuccess"),
			displayName, amount.Abs().String())
	}

	// Add admin center keyboard
	keyboard := BuildAdminCenterKeyboard(ctx)
	successText = fmt.Sprintf("%s\n\n%s", successText, service.I18n().T(ctx, "AdminCenterTitle"))

	return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, successText, keyboard)
}

// Helper functions

// getTenantBot gets the bot instance for the current tenant
func getTenantBot(ctx context.Context) (*tgbotapi.BotAPI, error) {
	tenantId, ok := middleware.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, gerror.New("tenantId not found in context")
	}

	return service.TenantBotManager().GetBot(ctx, tenantId)
}

func deleteUserMessage(ctx context.Context, message *tgbotapi.Message) {
	bot, err := getTenantBot(ctx)
	if err != nil {
		return
	}

	deleteMsg := tgbotapi.NewDeleteMessage(message.Chat.ID, message.MessageID)
	_, _ = bot.Request(deleteMsg)
}

func sendErrorAndContinue(ctx context.Context, chatID int64, userState *model.UserState, errorText string) (bool, error) {
	// Get the stored message ID from context
	messageID := 0
	if userState.Context != nil {
		if msgIDStr, exists := userState.Context["message_id"]; exists {
			messageID, _ = strconv.Atoi(msgIDStr)
		}
	}

	// Build back to admin keyboard
	keyboard := BuildBackToAdminKeyboard(ctx)

	// Get bot instance
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	// Add retry instruction to error message
	fullErrorText := errorText + "\n\n" + service.I18n().T(ctx, "AdminRetryInputPrompt")

	if messageID > 0 {
		editMsg := tgbotapi.NewEditMessageText(chatID, messageID, fullErrorText)
		editMsg.ParseMode = "HTML"
		editMsg.ReplyMarkup = &keyboard
		_, _ = bot.Send(editMsg)
	} else {
		// Fallback to sending new message
		msg := tgbotapi.NewMessage(chatID, fullErrorText)
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = keyboard
		_, _ = bot.Send(msg)
	}

	// NOTE: We do NOT clear the user state here - this allows the user to retry input
	return true, nil
}

// sendErrorAndKeepState sends an error message while preserving the user's input state
func sendErrorAndKeepState(ctx context.Context, chatID int64, userState *model.UserState, errorText string) (bool, error) {
	// Get the stored message ID from context
	messageID := 0
	if userState.Context != nil {
		if msgIDStr, exists := userState.Context["message_id"]; exists {
			messageID, _ = strconv.Atoi(msgIDStr)
		}
	}

	// Build back to admin keyboard
	keyboard := BuildBackToAdminKeyboard(ctx)

	// Get bot instance
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	// Add retry instruction to error message
	fullErrorText := errorText + "\n\n" + service.I18n().T(ctx, "AdminRetryInputPrompt")

	if messageID > 0 {
		editMsg := tgbotapi.NewEditMessageText(chatID, messageID, fullErrorText)
		editMsg.ParseMode = "HTML"
		editMsg.ReplyMarkup = &keyboard
		_, _ = bot.Send(editMsg)
	} else {
		// Fallback to sending new message
		msg := tgbotapi.NewMessage(chatID, fullErrorText)
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = keyboard
		_, _ = bot.Send(msg)
	}

	// NOTE: We do NOT clear the user state here - this allows the user to retry input
	// The state will remain active until successful completion or TTL expiration
	return true, nil
}

// sendErrorAndKeepStateWithUserID sends an error message while preserving the user's input state
// and updates the retry count in Redis
func sendErrorAndKeepStateWithUserID(ctx context.Context, chatID int64, telegramUserID int64, userState *model.UserState, errorText string) (bool, error) {
	// Get the stored message ID from context
	messageID := 0
	if userState.Context != nil {
		if msgIDStr, exists := userState.Context["message_id"]; exists {
			messageID, _ = strconv.Atoi(msgIDStr)
		}
	}

	// Build back to admin keyboard
	keyboard := BuildBackToAdminKeyboard(ctx)

	// Get bot instance
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	// Add retry instruction to error message
	fullErrorText := errorText + "\n\n" + service.I18n().T(ctx, "AdminRetryInputPrompt")

	if messageID > 0 {
		editMsg := tgbotapi.NewEditMessageText(chatID, messageID, fullErrorText)
		editMsg.ParseMode = "HTML"
		editMsg.ReplyMarkup = &keyboard
		_, _ = bot.Send(editMsg)
	} else {
		// Fallback to sending new message
		msg := tgbotapi.NewMessage(chatID, fullErrorText)
		msg.ParseMode = "HTML"
		msg.ReplyMarkup = keyboard
		_, _ = bot.Send(msg)
	}

	// Increment retry count and update state in Redis
	if userState != nil {
		userState.RetryCount++
		err = service.UserState().SetUserStateByTelegramId(ctx, telegramUserID, userState)
		if err != nil {
			g.Log().Errorf(ctx, "Failed to update user state retry count: %v", err)
		}
	}

	return true, nil
}

// getUserByTelegramUsername 通过Telegram用户名从user_backup_accounts表查询用户
func getUserByTelegramUsername(ctx context.Context, username string) (*entity.Users, error) {
	// Get tenant ID from context
	tenantId, ok := tenant.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, gerror.New("tenant ID not found in context")
	}

	// Query user_backup_accounts table to get user_id
	var backupAccount *entity.UserBackupAccounts
	err := dao.UserBackupAccounts.Ctx(ctx).
		Where("tenant_id = ?", tenantId).
		Where("telegram_username = ?", username).
		Where("deleted_at IS NULL").
		Scan(&backupAccount)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to query user_backup_accounts: %v", err)
		return nil, err
	}

	if backupAccount == nil {
		return nil, gerror.New("user not found")
	}

	// Get user by user_id
	targetUser, err := service.User().GetUserByUserId(ctx, backupAccount.UserId)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user by ID %d: %v", backupAccount.UserId, err)
		return nil, err
	}

	return targetUser, nil
}

// handleManualBalanceInputNew processes manual balance adjustment input with new interactive interface
func handleManualBalanceInputNew(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Parse input format: @username amount [type] or userid amount [type]
	// type can be "彩金" for bonus flow, otherwise it's regular balance
	input := strings.TrimSpace(message.Text)

	// Step 1: Format validation
	parts := strings.Fields(input)
	if len(parts) < 2 || len(parts) > 3 {
		// Format error - keep state and show error with retry prompt
		return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminBalanceFormatErrorStrict"))
	}

	userIdentifier := parts[0]
	amountStr := parts[1]
	var operationType string
	if len(parts) == 3 {
		operationType = strings.TrimSpace(parts[2])
		// Validate operation type - only allow "彩金" or empty
		if operationType != "彩金" {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminInvalidOperationType"))
		}
	}

	// Validate amount format
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminFormatError"))
	}

	// Validate amount using utility function (for positive amounts)
	// For negative amounts, we validate the absolute value
	amountToValidate := amount
	if amount.IsNegative() {
		amountToValidate = amount.Abs()
	}

	// Skip zero validation for admin operations (allow zero for testing)
	if !amountToValidate.IsZero() {
		isValid, errorKey := utility.ValidateAmountDecimal(ctx, amountToValidate, "CNY")
		if !isValid {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, errorKey))
		}
	}

	// Step 2: User existence validation
	var targetUser *entity.Users

	// Check if it's a username (@username) or Telegram ID
	if strings.HasPrefix(userIdentifier, "@") {
		// Username format - query from user_backup_accounts table
		username := userIdentifier // Keep the @ prefix for exact match
		targetUser, err = getUserByTelegramUsername(ctx, username)
		if err != nil {
			// Return the specific error message instead of generic "user not found"
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+err.Error())
		}
		if targetUser == nil {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
	} else {
		// Try to parse as Telegram ID
		telegramID, err := strconv.ParseInt(userIdentifier, 10, 64)
		if err != nil {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+service.I18n().T(ctx, "AdminFormatError"))
		}

		targetUser, err = service.User().GetUserByTelegramId(ctx, telegramID)
		if err != nil {
			// Return the specific error message instead of generic "user not found"
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+err.Error())
		}
		if targetUser == nil {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
	}

	// Step 3: Balance sufficiency validation (conditional execution)
	isBonusFlow := operationType == "彩金"
	if !isBonusFlow && amount.IsNegative() {
		// Check if user has sufficient balance for deduction
		balanceInfo, err := wallet.Manager().GetBalance(ctx, targetUser.Id, "CNY")
		if err != nil {
			g.Log().Errorf(ctx, "Failed to get user balance: %v", err)
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminSystemError"))
		}

		if balanceInfo.AvailableBalance.LessThan(amount.Abs()) {
			// Send insufficient balance message (not a popup)
			displayName, _ := service.User().GetUserDisplayName(ctx, targetUser.Id)
			currentBalance := service.Token().FormatUserBalance(ctx, balanceInfo.AvailableBalance, "CNY").String()

			text := fmt.Sprintf(service.I18n().T(ctx, "AdminInsufficientBalanceError"), displayName, currentBalance)

			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, text)
		}
	}

	// Clear user state before processing
	err = service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Step 4: Execute the operation
	description := service.I18n().T(ctx, "AdminDefaultIncreaseReason")
	if amount.IsNegative() {
		description = service.I18n().T(ctx, "AdminDefaultDecreaseReason")
	}

	var adjustErr error
	if isBonusFlow {
		// For bonus: only allow positive amounts
		if !amount.IsPositive() {
			text := "彩金只能增加，不能减少"
			return sendAdminMessage(ctx, message.Chat.ID, text)
		}
		// Process bonus: add to balance and flow requirement (6x)
		bonusDescription := description + "__BONUS__"
		adjustErr = service.Admin().AdjustUserBalance(ctx, targetUser.Id, amount.String(), bonusDescription)
	} else {
		// Adjust balance
		adjustErr = service.Admin().AdjustUserBalance(ctx, targetUser.Id, amount.String(), description)
	}

	if adjustErr != nil {
		// Send error message
		text := fmt.Sprintf("%s\n\n%s",
			service.I18n().T(ctx, "AdminAdjustmentFailed"),
			adjustErr.Error())
		return sendAdminMessage(ctx, message.Chat.ID, text)
	}

	// Send success message as a new message
	displayName, _ := service.User().GetUserDisplayName(ctx, targetUser.Id)

	if isBonusFlow {
		// Handle bonus operation success
		// Get updated balance for display
		balanceInfo, _ := wallet.Manager().GetBalance(ctx, targetUser.Id, "CNY")
		currentBalance := "0"
		if balanceInfo != nil {
			currentBalance = service.Token().FormatUserBalance(ctx, balanceInfo.AvailableBalance, "CNY").String()
		}

		// Calculate flow requirement added (6x bonus amount)
		flowAdded := amount.Mul(decimal.NewFromInt(6))

		successText := fmt.Sprintf("🎁 彩金发放成功！\n\n"+
			"👤 用户：%s\n"+
			"💰 彩金金额：%s CNY\n"+
			"💳 当前余额：%s CNY\n"+
			"🔄 增加流水要求：%s CNY",
			displayName, amount.String(), currentBalance, flowAdded.String())

		// Add return button
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(
				tgbotapi.NewInlineKeyboardButtonData("🔙 返回管理员中心", "admin_center"),
			),
		)
		return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, successText, keyboard)
	} else {
		// Handle balance adjustment success
		// Get updated balance for display
		balanceInfo, _ := wallet.Manager().GetBalance(ctx, targetUser.Id, "CNY")
		currentBalance := "0"
		if balanceInfo != nil {
			currentBalance = service.Token().FormatUserBalance(ctx, balanceInfo.AvailableBalance, "CNY").String()
		}

		var successText string
		if amount.IsPositive() {
			successText = fmt.Sprintf("%s 的 CNY 增加了 %s\n\n当前钱包余额：%s",
				displayName, amount.String(), currentBalance)
		} else {
			successText = fmt.Sprintf("%s 的 CNY 减少了 %s\n\n当前钱包余额：%s",
				displayName, amount.Abs().String(), currentBalance)
		}

		// Add return button
		keyboard := tgbotapi.NewInlineKeyboardMarkup(
			tgbotapi.NewInlineKeyboardRow(
				tgbotapi.NewInlineKeyboardButtonData("🔙 返回管理员中心", "admin_center"),
			),
		)
		return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, successText, keyboard)
	}
}

func sendAdminMessage(ctx context.Context, chatID int64, text string) (bool, error) {
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"
	_, err = bot.Send(msg)

	return true, err
}

// handleWithdrawalRejectReasonInput processes withdrawal rejection reason input
func handleWithdrawalRejectReasonInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Get withdrawal ID from state
	withdrawalIDStr, ok := userState.Context["withdrawal_id"]
	if !ok {
		g.Log().Error(ctx, "withdrawal_id not found in user state")
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminSystemError"))
	}

	withdrawalID, err := strconv.ParseUint(withdrawalIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to parse withdrawal ID: %v", err)
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminSystemError"))
	}

	// Get rejection reason
	reason := strings.TrimSpace(message.Text)
	if reason == "" {
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminWithdrawalRejectReasonEmpty"))
	}

	// Clear user state
	err = service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Reject the withdrawal
	err = service.Admin().RejectWithdrawal(ctx, withdrawalID, reason)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to reject withdrawal %d: %v", withdrawalID, err)
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminWithdrawalRejectionError"))
	}

	// Send success message
	text := "❌ " + service.I18n().T(ctx, "AdminWithdrawalRejected")
	keyboard := BuildBackToAdminKeyboard(ctx)
	return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, text, keyboard)
}

// handleWithdrawalRejectBanReasonInput processes withdrawal rejection with ban reason input
func handleWithdrawalRejectBanReasonInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Get withdrawal ID from state
	withdrawalIDStr, ok := userState.Context["withdrawal_id"]
	if !ok {
		g.Log().Error(ctx, "withdrawal_id not found in user state")
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminSystemError"))
	}

	withdrawalID, err := strconv.ParseUint(withdrawalIDStr, 10, 64)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to parse withdrawal ID: %v", err)
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminSystemError"))
	}

	// Get rejection reason
	reason := strings.TrimSpace(message.Text)
	if reason == "" {
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminWithdrawalRejectReasonEmpty"))
	}

	// Clear user state
	err = service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Reject the withdrawal and ban user
	err = service.Admin().RejectWithdrawalAndBanUser(ctx, withdrawalID, reason)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to reject withdrawal and ban user %d: %v", withdrawalID, err)
		return sendAdminMessage(ctx, message.Chat.ID, service.I18n().T(ctx, "AdminWithdrawalRejectionError"))
	}

	// Send success message
	text := "🚫 " + service.I18n().T(ctx, "AdminWithdrawalRejectedAndBanned")
	keyboard := BuildBackToAdminKeyboard(ctx)
	return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, text, keyboard)
}

func sendAdminMessageWithKeyboard(ctx context.Context, chatID int64, text string, keyboard tgbotapi.InlineKeyboardMarkup) (bool, error) {
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	msg := tgbotapi.NewMessage(chatID, text)
	msg.ParseMode = "HTML"
	msg.ReplyMarkup = keyboard
	_, err = bot.Send(msg)

	return true, err
}

// editAdminMessageWithKeyboard edits an existing message with new text and keyboard
func editAdminMessageWithKeyboard(ctx context.Context, chatID int64, messageID int, text string, keyboard tgbotapi.InlineKeyboardMarkup) (bool, error) {
	bot, err := getTenantBot(ctx)
	if err != nil {
		return true, err
	}

	// If messageID is 0 or invalid, fallback to sending new message
	if messageID <= 0 {
		return sendAdminMessageWithKeyboard(ctx, chatID, text, keyboard)
	}

	editMsg := tgbotapi.NewEditMessageText(chatID, messageID, text)
	editMsg.ParseMode = "HTML"
	editMsg.ReplyMarkup = &keyboard
	_, err = bot.Send(editMsg)

	// If edit fails (e.g., message too old), fallback to sending new message
	if err != nil {
		g.Log().Warningf(ctx, "Failed to edit message %d, falling back to new message: %v", messageID, err)
		return sendAdminMessageWithKeyboard(ctx, chatID, text, keyboard)
	}

	return true, nil
}

// isNumeric checks if a string is numeric
func isNumeric(s string) bool {
	s = strings.TrimSpace(s)
	if s == "" {
		return false
	}
	_, err := strconv.ParseInt(s, 10, 64)
	return err == nil
}

// handlePersonalStatsSearchInput handles personal stats search input when in search state
func handlePersonalStatsSearchInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Clear user state
	err := service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Get original message ID from user state
	originalMessageID := 0
	if messageIDStr, exists := userState.Context["message_id"]; exists {
		if parsedID, parseErr := strconv.Atoi(messageIDStr); parseErr == nil {
			originalMessageID = parsedID
		}
	}

	// Search for user
	query := strings.TrimSpace(message.Text)
	user, err := service.PersonalStatsService().SearchUser(ctx, query)
	if err != nil {
		// Edit original message with error
		errorText := service.I18n().T(ctx, "AdminPersonalStatsUserNotFound")
		keyboard := BuildPersonalStatsKeyboard(ctx)
		return editAdminMessageWithKeyboard(ctx, message.Chat.ID, originalMessageID, errorText, keyboard)
	}

	// Get detailed statistics
	stats, err := service.PersonalStatsService().GetUserDetailedStats(ctx, user.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user detailed stats: %v", err)
		errorText := service.I18n().T(ctx, "AdminPersonalStatsError")
		keyboard := BuildPersonalStatsKeyboard(ctx)
		return editAdminMessageWithKeyboard(ctx, message.Chat.ID, originalMessageID, errorText, keyboard)
	}

	// Build and edit original message with results
	text := BuildUserDetailedStatsMessage(ctx, stats)
	keyboard := BuildUserStatsKeyboard(ctx, user.Id, stats.Status)

	return editAdminMessageWithKeyboard(ctx, message.Chat.ID, originalMessageID, text, keyboard)
}

// handleDirectPersonalStatsSearch handles direct personal stats search without state
func handleDirectPersonalStatsSearch(ctx context.Context, message *tgbotapi.Message) (bool, error) {
	// Search for user first, only delete message if successful
	query := strings.TrimSpace(message.Text)
	user, err := service.PersonalStatsService().SearchUser(ctx, query)
	if err != nil {
		// Not found, don't handle and don't delete message
		return false, nil
	}

	// Delete user's message only after successful user search
	deleteUserMessage(ctx, message)

	// Get detailed statistics
	stats, err := service.PersonalStatsService().GetUserDetailedStats(ctx, user.Id)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get user detailed stats: %v", err)
		return false, nil
	}

	// Build and send message
	text := BuildUserDetailedStatsMessage(ctx, stats)
	keyboard := BuildUserStatsKeyboard(ctx, user.Id, stats.Status)

	return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, text, keyboard)
}

// handleFlowRequirementsInputNew processes flow requirements adjustment input with new interactive interface
func handleFlowRequirementsInputNew(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Parse input format: @username amount or userid amount
	input := strings.TrimSpace(message.Text)

	// Step 1: Format validation - must be exactly 2 parts
	parts := strings.Fields(input)
	if len(parts) != 2 {
		// Format error - show specific error message for flow requirements
		return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminInvalidFlowFormatStrict"))
	}

	userIdentifier := parts[0]
	amountStr := parts[1]

	// Validate amount format
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminFormatError"))
	}

	// Validate amount using utility function (for positive amounts)
	// For negative amounts, we validate the absolute value
	amountToValidate := amount
	if amount.IsNegative() {
		amountToValidate = amount.Abs()
	}

	// Skip zero validation for admin operations (allow zero for testing)
	if !amountToValidate.IsZero() {
		isValid, errorKey := utility.ValidateAmountDecimal(ctx, amountToValidate, "CNY")
		if !isValid {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+service.I18n().T(ctx, errorKey))
		}
	}

	// Step 2: User existence validation
	var targetUser *entity.Users

	// Check if it's a username (@username) or Telegram ID
	if strings.HasPrefix(userIdentifier, "@") {
		// Username format - query from user_backup_accounts table
		username := userIdentifier // Keep the @ prefix for exact match
		targetUser, err = getUserByTelegramUsername(ctx, username)
		if err != nil {
			// Return the specific error message instead of generic "user not found"
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, err.Error())
		}
		if targetUser == nil {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
	} else {
		// Try to parse as Telegram ID
		telegramID, err := strconv.ParseInt(userIdentifier, 10, 64)
		if err != nil {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, service.I18n().T(ctx, "AdminFormatError"))
		}

		targetUser, err = service.User().GetUserByTelegramId(ctx, telegramID)
		if err != nil {
			// Return the specific error message instead of generic "user not found"
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+err.Error())
		}
		if targetUser == nil {
			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, "❌"+service.I18n().T(ctx, "AdminUserNotFoundError"))
		}
	}

	// Step 3: Flow requirement sufficiency validation (conditional execution)
	if amount.IsNegative() {
		// Check if user has sufficient flow requirement for deduction
		currentFlow := targetUser.WithdrawBettingVolume

		if currentFlow.LessThan(amount.Abs()) {
			// Send insufficient flow requirement message
			displayName, _ := service.User().GetUserDisplayName(ctx, targetUser.Id)

			text := fmt.Sprintf("❌ 操作失败：用户的流水要求不足\n\n%s 的流水要求：%s",
				displayName, currentFlow.String())

			return sendErrorAndKeepStateWithUserID(ctx, message.Chat.ID, message.From.ID, userState, text)
		}
	}

	// Clear user state before processing
	err = service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to clear user state: %v", err)
	}

	// Step 4: Execute the operation
	description := service.I18n().T(ctx, "AdminDefaultIncreaseReason")
	if amount.IsNegative() {
		description = service.I18n().T(ctx, "AdminDefaultDecreaseReason")
	}

	adjustErr := service.Admin().AdjustUserFlowRequirement(ctx, targetUser.Id, amount.String(), description)
	if adjustErr != nil {
		// Send error message
		text := fmt.Sprintf("❌%s\n\n%s",
			service.I18n().T(ctx, "AdminAdjustmentFailed"),
			adjustErr.Error())
		return sendAdminMessage(ctx, message.Chat.ID, text)
	}

	// Send success message as a new message
	displayName, _ := service.User().GetUserDisplayName(ctx, targetUser.Id)

	// Get updated flow requirement for display
	updatedUser, _ := service.User().GetUserByUserId(ctx, targetUser.Id)
	currentFlow := "0"
	if updatedUser != nil {
		currentFlow = updatedUser.WithdrawBettingVolume.String()
	}

	var successText string
	if amount.IsPositive() {
		successText = fmt.Sprintf("%s 的流水要求增加了 %s\n\n当前剩余流水要求：%s",
			displayName, amount.String(), currentFlow)
	} else {
		successText = fmt.Sprintf("%s 的流水要求减少了 %s\n\n当前剩余流水要求：%s",
			displayName, amount.Abs().String(), currentFlow)
	}

	// Add return button
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("🔙 返回管理员中心", "admin_center"),
		),
	)
	return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, successText, keyboard)
}

// handleBettingVolumeDateInput handles date range input for betting volume query
func handleBettingVolumeDateInput(ctx context.Context, message *tgbotapi.Message, userState *model.UserState) (bool, error) {
	// Delete user's message to keep chat clean
	deleteUserMessage(ctx, message)

	// Parse the date range input
	dateInput := strings.TrimSpace(message.Text)
	parts := strings.Fields(dateInput)

	// Validate input format
	if len(parts) != 2 {
		// Clear user state and show error
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 格式错误")
	}

	startDateStr := parts[0]
	endDateStr := parts[1]

	// Validate date format (YYYY-MM-DD)
	dateRegex := regexp.MustCompile(`^\d{4}-\d{2}-\d{2}$`)
	if !dateRegex.MatchString(startDateStr) || !dateRegex.MatchString(endDateStr) {
		// Clear user state and show error
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 格式错误")
	}

	// Parse dates
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 格式错误")
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 格式错误")
	}

	// Validate date range
	if startDate.After(endDate) {
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 开始日期需小于结束日期")
	}

	// Get user ID and message info from state context
	userIDStr := userState.Context["user_id"]
	messageIDStr := userState.Context["message_id"]
	chatIDStr := userState.Context["chat_id"]

	userID, err := strconv.ParseUint(userIDStr, 10, 64)
	if err != nil {
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 系统错误")
	}

	messageID, err := strconv.Atoi(messageIDStr)
	if err != nil {
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 系统错误")
	}

	chatID, err := strconv.ParseInt(chatIDStr, 10, 64)
	if err != nil {
		service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 系统错误")
	}

	// Clear user state
	service.UserState().ClearUserStateByTelegramId(ctx, message.From.ID)

	// Query betting volume data and display results
	return handleBettingVolumeResults(ctx, message, userID, startDateStr, endDateStr, chatID, messageID)
}

// handleBettingVolumeResults queries and displays betting volume results
func handleBettingVolumeResults(ctx context.Context, message *tgbotapi.Message, userID uint64, startDateStr, endDateStr string, chatID int64, messageID int) (bool, error) {
	// Check if user exists
	_, err := service.User().GetUserByUserId(ctx, userID)
	if err != nil {
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 用户不存在")
	}

	// Get user's telegram info for first name
	telegramID, err := service.BackupAccounts().GetTelegramIdByUserId(ctx, userID)
	if err != nil {
		g.Log().Warningf(ctx, "Failed to get telegram ID for user %d: %v", userID, err)
	}

	userFirstName := "Unknown"
	if telegramID != 0 {
		// Try to get display name
		displayName, err := service.User().GetUserDisplayName(ctx, userID)
		if err == nil && displayName != "" {
			userFirstName = displayName
		}
	}

	// Parse dates for querying
	startDate, _ := time.Parse("2006-01-02", startDateStr)
	endDate, _ := time.Parse("2006-01-02", endDateStr)

	// Convert to gtime for service calls
	startGTime := gtime.New(startDate)
	endGTime := gtime.New(endDate.Add(24 * time.Hour)) // Include the end date

	// Query betting volume by game provider
	bettingVolumes, err := getBettingVolumeByProvider(ctx, userID, startGTime, endGTime)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get betting volume: %v", err)
		return sendAdminMessage(ctx, message.Chat.ID, "❌ 查询失败")
	}

	// Build result message
	text := buildBettingVolumeResultMessage(userFirstName, startDateStr, endDateStr, bettingVolumes)

	// Build return keyboard
	keyboard := tgbotapi.NewInlineKeyboardMarkup(
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData(
				"📊 返回个人详细统计",
				fmt.Sprintf("admin_personal_detail:%d", userID),
			),
		),
	)

	// Edit the existing message instead of sending a new one
	bot, err := getTenantBot(ctx)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get bot: %v", err)
		// Fallback to sending new message
		return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, text, keyboard)
	}

	editMsg := tgbotapi.NewEditMessageText(chatID, messageID, text)
	editMsg.ParseMode = "HTML"
	editMsg.ReplyMarkup = &keyboard

	_, err = bot.Send(editMsg)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to edit message: %v", err)
		// Fallback to sending new message
		return sendAdminMessageWithKeyboard(ctx, message.Chat.ID, text, keyboard)
	}

	return true, nil
}

// BettingVolumeData represents betting volume for a specific game provider
type BettingVolumeData struct {
	ProviderName string
	Volume       decimal.Decimal
}

// getBettingVolumeByProvider queries betting volume by game provider for a user within date range
func getBettingVolumeByProvider(ctx context.Context, userID uint64, startDate, endDate *gtime.Time) ([]BettingVolumeData, error) {
	// Get tenant ID from context
	tenantId, ok := tenant.GetTenantIdFromContext(ctx)
	if !ok {
		return nil, gerror.New("tenant ID not found in context")
	}

	var results []BettingVolumeData

	// Get the list of unique providers from game_catalog with their display names
	var providerTypes []struct {
		ProductType string `json:"product_type"`
		GameName    string `json:"game_name"`
	}

	err := g.DB().Model("game_catalog").
		Fields("DISTINCT product_type, game_name").
		Where("display_status = ?", 1).
		Where("product_type IS NOT NULL AND product_type != ''").
		Scan(&providerTypes)
	if err != nil {
		return nil, gerror.Wrap(err, "failed to get provider types")
	}

	// For each provider, get the betting volume
	for _, provider := range providerTypes {
		volume, err := getBettingVolumeForProvider(ctx, tenantId, userID, provider.ProductType, startDate, endDate)
		if err != nil {
			g.Log().Warningf(ctx, "Failed to get betting volume for provider %s: %v", provider.ProductType, err)
			volume = decimal.Zero
		}

		// Use the product_type as display name (e.g., "EG5", "PP", "PG")
		providerName := provider.ProductType
		if providerName == "" {
			providerName = "Unknown"
		}

		results = append(results, BettingVolumeData{
			ProviderName: providerName,
			Volume:       volume,
		})
	}

	return results, nil
}

// getBettingVolumeForProvider gets betting volume for a specific provider
func getBettingVolumeForProvider(ctx context.Context, tenantId uint, userID uint64, providerCode string, startDate, endDate *gtime.Time) (decimal.Decimal, error) {
	var result struct {
		Total decimal.Decimal `json:"total"`
	}

	// Query the database for the sum of bet amounts for this user and provider
	// Use LIKE query on game_code to match games by provider (e.g., game_code LIKE 'EG5%')
	err := g.DB().Model(dao.GameTransactionRecords.Table()).
		Fields("COALESCE(SUM(amount), 0) as total").
		Where("tenant_id = ?", tenantId).
		Where("user_id = ?", userID).
		Where("game_code LIKE ?", providerCode+"%").
		Where("created_at >= ? AND created_at < ?", startDate, endDate).
		Where("type = ?", "bet").
		Where("status = ?", "completed").
		Scan(&result)

	if err != nil {
		return decimal.Zero, gerror.Wrapf(err, "failed to get betting volume for user %d provider %s", userID, providerCode)
	}

	return result.Total, nil
}

// buildBettingVolumeResultMessage builds the result message for betting volume query
func buildBettingVolumeResultMessage(userFirstName, startDate, endDate string, volumes []BettingVolumeData) string {
	var msg strings.Builder

	msg.WriteString(fmt.Sprintf("查询用户：%s\n", userFirstName))
	msg.WriteString(fmt.Sprintf("开始日期：%s\n", startDate))
	msg.WriteString(fmt.Sprintf("结束日期：%s\n\n", endDate))

	for _, volume := range volumes {
		msg.WriteString(fmt.Sprintf("🎮 %s流水：%s\n", volume.ProviderName, volume.Volume.StringFixed(2)))
	}

	return msg.String()
}
